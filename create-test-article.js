const axios = require('axios');

async function createTestArticle() {
    try {
        console.log('📝 Creating test article for categoryId testing...');
        
        // 1. Login
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        const token = loginResponse.data.accessToken;
        console.log('✅ Login successful');
        
        // 2. Get categories first
        console.log('\n2. Getting categories...');
        const categoriesResponse = await axios.get('http://localhost:3000/admin/news/categories', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log(`🗂️ Found ${categoriesResponse.data.meta.totalItems} categories:`);
        categoriesResponse.data.data.forEach(category => {
            console.log(`  - ID: ${category.id}, Name: "${category.name}"`);
        });
        
        if (categoriesResponse.data.data.length === 0) {
            console.log('❌ No categories found. Cannot create article.');
            return;
        }
        
        const firstCategory = categoriesResponse.data.data[0];
        
        // 3. Create test article
        console.log(`\n3. Creating test article with category ${firstCategory.id}...`);
        
        const articleData = {
            title: 'Test Article for CategoryId Update',
            excerpt: 'This is a test article created to test categoryId update functionality',
            content: '<p>This article is created specifically for testing the categoryId update feature. It should work perfectly after our fixes.</p>',
            tags: ['test', 'categoryid', 'update'],
            status: 'published',
            categoryId: firstCategory.id,
            isFeatured: false,
            priority: 1,
            metaTitle: 'Test Article - CategoryId Update',
            metaDescription: 'Test article for categoryId update functionality'
        };
        
        const createResponse = await axios.post(
            'http://localhost:3000/admin/news/articles',
            articleData,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ Test article created successfully!');
        console.log(`  Article ID: ${createResponse.data.id}`);
        console.log(`  Title: "${createResponse.data.title}"`);
        console.log(`  CategoryId: ${createResponse.data.categoryId}`);
        console.log(`  Category Name: ${createResponse.data.category?.name}`);
        
        // 4. Now test categoryId update
        if (categoriesResponse.data.data.length > 1) {
            const targetCategory = categoriesResponse.data.data[1];
            
            console.log(`\n4. Testing categoryId update from ${firstCategory.id} to ${targetCategory.id}...`);
            
            const updateResponse = await axios.patch(
                `http://localhost:3000/admin/news/articles/${createResponse.data.id}`,
                { categoryId: targetCategory.id },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            console.log('✅ CategoryId update successful!');
            console.log(`  New CategoryId: ${updateResponse.data.categoryId}`);
            console.log(`  New Category Name: ${updateResponse.data.category?.name}`);
            console.log(`  Category ID from object: ${updateResponse.data.category?.id}`);
            
            // 5. Verify
            if (updateResponse.data.categoryId === targetCategory.id && 
                updateResponse.data.category?.id === targetCategory.id) {
                console.log('\n🎉 PERFECT! CategoryId update is working correctly!');
                console.log('✅ categoryId field is returned in response');
                console.log('✅ categoryId matches target category');
                console.log('✅ category object is correctly populated');
                
                console.log('\n🎯 YOUR COMMAND SHOULD WORK NOW:');
                console.log(`curl -X PATCH "http://localhost:3000/admin/news/articles/${createResponse.data.id}" \\`);
                console.log(`  -H "Content-Type: application/json" \\`);
                console.log(`  -H "Authorization: Bearer ${token}" \\`);
                console.log(`  -d '{"categoryId":${firstCategory.id}}'`);
                
            } else {
                console.log('\n❌ CategoryId update still has issues');
                console.log(`  Expected: ${targetCategory.id}`);
                console.log(`  Got categoryId: ${updateResponse.data.categoryId}`);
                console.log(`  Got category.id: ${updateResponse.data.category?.id}`);
            }
        } else {
            console.log('\n⚠️ Only one category available, cannot test category change');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

createTestArticle();
