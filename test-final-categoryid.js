const axios = require('axios');

async function testFinalCategoryId() {
    try {
        console.log('🎯 FINAL TEST: Category ID Update');
        console.log('=' * 50);
        
        // 1. Login
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        const token = loginResponse.data.accessToken;
        console.log('✅ Login successful');
        
        // 2. Get all articles
        console.log('\n2. Getting all articles...');
        const articlesResponse = await axios.get('http://localhost:3000/admin/news/articles', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log(`📰 Found ${articlesResponse.data.meta.totalItems} articles:`);
        articlesResponse.data.data.forEach(article => {
            console.log(`  - ID: ${article.id}, Title: "${article.title}"`);
            console.log(`    CategoryId: ${article.categoryId}, Category: ${article.category?.name}`);
        });
        
        if (articlesResponse.data.data.length === 0) {
            console.log('❌ No articles found to test');
            return;
        }
        
        const testArticle = articlesResponse.data.data[0];
        console.log(`\n🎯 Testing with Article ID: ${testArticle.id}`);
        
        // 3. Get all categories
        console.log('\n3. Getting all categories...');
        const categoriesResponse = await axios.get('http://localhost:3000/admin/news/categories', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log(`🗂️ Found ${categoriesResponse.data.meta.totalItems} categories:`);
        categoriesResponse.data.data.forEach(category => {
            console.log(`  - ID: ${category.id}, Name: "${category.name}"`);
        });
        
        // 4. Test category update
        const targetCategoryId = categoriesResponse.data.data.find(cat => cat.id !== testArticle.categoryId)?.id || 1;
        
        console.log(`\n4. Updating article ${testArticle.id} categoryId from ${testArticle.categoryId} to ${targetCategoryId}...`);
        
        const updateResponse = await axios.patch(
            `http://localhost:3000/admin/news/articles/${testArticle.id}`,
            { categoryId: targetCategoryId },
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ Update successful!');
        console.log('Response data:');
        console.log(`  Article ID: ${updateResponse.data.id}`);
        console.log(`  Title: "${updateResponse.data.title}"`);
        console.log(`  CategoryId: ${updateResponse.data.categoryId}`);
        console.log(`  Category Name: ${updateResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${updateResponse.data.category?.id}`);
        console.log(`  Updated At: ${updateResponse.data.updatedAt}`);
        
        // 5. Verify the update
        console.log('\n5. Verifying update by fetching article again...');
        const verifyResponse = await axios.get(
            `http://localhost:3000/admin/news/articles/${testArticle.id}`,
            {
                headers: { 'Authorization': `Bearer ${token}` }
            }
        );
        
        console.log('Verification result:');
        console.log(`  CategoryId: ${verifyResponse.data.categoryId}`);
        console.log(`  Category Name: ${verifyResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${verifyResponse.data.category?.id}`);
        
        // 6. Final validation
        const success = verifyResponse.data.categoryId === targetCategoryId && 
                       verifyResponse.data.category?.id === targetCategoryId;
        
        if (success) {
            console.log('\n🎉 SUCCESS! CategoryId update is working perfectly!');
            console.log('✅ categoryId field is correctly returned in response');
            console.log('✅ categoryId matches the target category');
            console.log('✅ category object is correctly populated');
            console.log('✅ Database update is working');
        } else {
            console.log('\n❌ FAILURE! CategoryId update has issues:');
            console.log(`  Expected categoryId: ${targetCategoryId}`);
            console.log(`  Actual categoryId: ${verifyResponse.data.categoryId}`);
            console.log(`  Category object ID: ${verifyResponse.data.category?.id}`);
        }
        
        // 7. Test your original command format
        console.log('\n7. Testing your original command format...');
        const originalTestResponse = await axios.patch(
            `http://localhost:3000/admin/news/articles/${testArticle.id}`,
            { categoryId: 1 },
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('Original command test result:');
        console.log(`  CategoryId: ${originalTestResponse.data.categoryId}`);
        console.log(`  Category Name: ${originalTestResponse.data.category?.name}`);
        
        if (originalTestResponse.data.categoryId === 1) {
            console.log('✅ Your original command format works perfectly!');
        } else {
            console.log('❌ Your original command format still has issues');
        }
        
        console.log('\n' + '='.repeat(50));
        console.log('🎯 CORRECTED COMMAND FOR YOUR USE:');
        console.log('='.repeat(50));
        console.log(`curl -X PATCH "http://localhost:3000/admin/news/articles/${testArticle.id}" \\`);
        console.log(`  -H "Content-Type: application/json" \\`);
        console.log(`  -H "Authorization: Bearer ${token}" \\`);
        console.log(`  -d '{"categoryId":2}'`);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

testFinalCategoryId();
