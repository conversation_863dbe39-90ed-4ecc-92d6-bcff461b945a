const { Client } = require('pg');

async function checkDatabase() {
    const client = new Client({
        host: 'localhost',
        user: 'postgresuser',
        password: '831993da',
        database: 'testlivesport',
        port: 5432,
    });

    try {
        await client.connect();
        console.log('✅ Connected to database');

        // Check article ID 7 before update
        console.log('\n1. Checking article ID 7 in database:');
        const articleResult = await client.query(
            'SELECT id, title, "categoryId", status, "updatedAt" FROM news_articles WHERE id = 7'
        );
        
        if (articleResult.rows.length === 0) {
            console.log('❌ Article ID 7 not found in database');
            return;
        }
        
        const article = articleResult.rows[0];
        console.log('Article data:', article);
        
        // Check categories
        console.log('\n2. Checking available categories:');
        const categoriesResult = await client.query(
            'SELECT id, name, slug FROM news_categories ORDER BY id'
        );
        
        console.log('Available categories:');
        categoriesResult.rows.forEach(cat => {
            console.log(`  ID: ${cat.id}, Name: "${cat.name}", Slug: "${cat.slug}"`);
        });
        
        // Try to update directly in database
        console.log('\n3. Updating article categoryId directly in database...');
        const updateResult = await client.query(
            'UPDATE news_articles SET "categoryId" = $1, "updatedAt" = NOW() WHERE id = $2 RETURNING id, "categoryId", "updatedAt"',
            [2, 7]
        );
        
        if (updateResult.rows.length > 0) {
            console.log('✅ Database update successful:', updateResult.rows[0]);
        } else {
            console.log('❌ Database update failed');
        }
        
        // Check the result
        console.log('\n4. Checking article after database update:');
        const afterUpdateResult = await client.query(
            'SELECT id, title, "categoryId", status, "updatedAt" FROM news_articles WHERE id = 7'
        );
        
        if (afterUpdateResult.rows.length > 0) {
            console.log('Article after update:', afterUpdateResult.rows[0]);
        }
        
        // Check with category join
        console.log('\n5. Checking with category join:');
        const joinResult = await client.query(`
            SELECT 
                a.id, 
                a.title, 
                a."categoryId", 
                c.name as category_name,
                c.slug as category_slug
            FROM news_articles a
            LEFT JOIN news_categories c ON a."categoryId" = c.id
            WHERE a.id = 7
        `);
        
        if (joinResult.rows.length > 0) {
            console.log('Article with category:', joinResult.rows[0]);
        }
        
    } catch (error) {
        console.error('❌ Database error:', error.message);
    } finally {
        await client.end();
    }
}

checkDatabase();
