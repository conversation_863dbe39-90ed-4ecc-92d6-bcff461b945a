const axios = require('axios');

async function debugArticleCategoryUpdate() {
    try {
        console.log('🔍 DEBUGGING ARTICLE CATEGORY UPDATE');
        console.log('=' * 50);
        
        // 1. Get fresh token
        console.log('1. Getting fresh token...');
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        const token = loginResponse.data.accessToken;
        console.log('✅ Fresh token obtained');
        console.log('Token (first 50 chars):', token.substring(0, 50) + '...');
        
        // 2. Check if article ID 7 exists
        console.log('\n2. Checking if article ID 7 exists...');
        try {
            const articleResponse = await axios.get('http://localhost:3000/admin/news/articles/7', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            console.log('✅ Article ID 7 exists:');
            console.log(`   Title: "${articleResponse.data.title}"`);
            console.log(`   Current Category ID: ${articleResponse.data.categoryId}`);
            console.log(`   Current Category: ${articleResponse.data.category ? articleResponse.data.category.name : 'None'}`);
            console.log(`   Status: ${articleResponse.data.status}`);
            
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('❌ Article ID 7 does not exist');
                
                // Show available articles
                const articlesResponse = await axios.get('http://localhost:3000/admin/news/articles', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                console.log('\n📰 Available Articles:');
                articlesResponse.data.data.forEach(article => {
                    console.log(`   ID: ${article.id}, Title: "${article.title}", Category: ${article.category?.name || 'None'}`);
                });
                return;
            } else {
                throw error;
            }
        }
        
        // 3. Check if category ID 2 exists
        console.log('\n3. Checking if category ID 2 exists...');
        try {
            const categoryResponse = await axios.get('http://localhost:3000/admin/news/categories/2', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            console.log('✅ Category ID 2 exists:');
            console.log(`   Name: "${categoryResponse.data.name}"`);
            console.log(`   Slug: "${categoryResponse.data.slug}"`);
            console.log(`   Active: ${categoryResponse.data.isActive}`);
            
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('❌ Category ID 2 does not exist');
                
                // Show available categories
                const categoriesResponse = await axios.get('http://localhost:3000/admin/news/categories', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                console.log('\n🗂️ Available Categories:');
                categoriesResponse.data.data.forEach(category => {
                    console.log(`   ID: ${category.id}, Name: "${category.name}", Slug: "${category.slug}"`);
                });
                return;
            } else {
                throw error;
            }
        }
        
        // 4. Test the exact command you used
        console.log('\n4. Testing your exact command...');
        console.log('Command: PATCH /admin/news/articles/7 with {"categoryId":2}');
        
        try {
            const updateResponse = await axios.patch(
                'http://localhost:3000/admin/news/articles/7',
                { categoryId: 2 },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            console.log('✅ Update successful!');
            console.log('Response data:');
            console.log(`   Title: "${updateResponse.data.title}"`);
            console.log(`   New Category ID: ${updateResponse.data.categoryId}`);
            console.log(`   New Category Name: ${updateResponse.data.category?.name}`);
            console.log(`   Updated At: ${updateResponse.data.updatedAt}`);
            
        } catch (error) {
            console.log('❌ Update failed!');
            console.log('Status:', error.response?.status);
            console.log('Error data:', JSON.stringify(error.response?.data, null, 2));
            
            // Additional debugging based on error type
            if (error.response?.status === 401) {
                console.log('\n🔍 401 Unauthorized - Token issue:');
                console.log('- Token might be expired');
                console.log('- Token format might be wrong');
                console.log('- User might not have permission');
                
            } else if (error.response?.status === 404) {
                console.log('\n🔍 404 Not Found:');
                console.log('- Article ID 7 might not exist');
                console.log('- Endpoint might be wrong');
                
            } else if (error.response?.status === 400) {
                console.log('\n🔍 400 Bad Request:');
                console.log('- Category ID 2 might not exist');
                console.log('- Data format might be wrong');
                console.log('- Validation error');
                
            } else if (error.response?.status === 500) {
                console.log('\n🔍 500 Internal Server Error:');
                console.log('- Database issue');
                console.log('- Server error');
                console.log('- Check server logs');
            }
        }
        
        // 5. Test with different approaches
        console.log('\n5. Testing alternative approaches...');
        
        // Try with string categoryId
        console.log('\n5a. Testing with string categoryId...');
        try {
            await axios.patch(
                'http://localhost:3000/admin/news/articles/7',
                { categoryId: "2" },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('✅ String categoryId works');
        } catch (error) {
            console.log('❌ String categoryId failed:', error.response?.status);
        }
        
        // Try with additional fields
        console.log('\n5b. Testing with additional fields...');
        try {
            await axios.patch(
                'http://localhost:3000/admin/news/articles/7',
                { 
                    categoryId: 2,
                    title: "Test Update with Category Change"
                },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('✅ Update with additional fields works');
        } catch (error) {
            console.log('❌ Update with additional fields failed:', error.response?.status);
        }
        
        // 6. Check server logs suggestion
        console.log('\n6. Debugging suggestions:');
        console.log('- Check PM2 logs: pm2 logs api-sports-game-3000');
        console.log('- Check database connection');
        console.log('- Verify article and category exist');
        console.log('- Check token expiration');
        
        console.log('\n' + '='.repeat(50));
        console.log('🎯 CORRECTED COMMAND:');
        console.log('='.repeat(50));
        console.log(`curl -X PATCH "http://localhost:3000/admin/news/articles/7" \\`);
        console.log(`  -H "Content-Type: application/json" \\`);
        console.log(`  -H "Authorization: Bearer ${token}" \\`);
        console.log(`  -d '{"categoryId":2}'`);
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

debugArticleCategoryUpdate();
