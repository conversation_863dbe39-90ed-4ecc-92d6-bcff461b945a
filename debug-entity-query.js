const { Client } = require('pg');

async function debugEntityQuery() {
    const client = new Client({
        host: 'localhost',
        user: 'postgresuser',
        password: '831993da',
        database: 'testlivesport',
        port: 5432,
    });

    try {
        await client.connect();
        console.log('🔍 Debugging Entity Query...');

        // 1. Check raw database data
        console.log('\n1. Raw database data for article 7:');
        const rawResult = await client.query(`
            SELECT 
                a.id,
                a.title,
                a."categoryId",
                a.status,
                a."updatedAt",
                c.id as category_id,
                c.name as category_name,
                c.slug as category_slug
            FROM news_articles a
            LEFT JOIN news_categories c ON a."categoryId" = c.id
            WHERE a.id = 7
        `);
        
        if (rawResult.rows.length > 0) {
            const row = rawResult.rows[0];
            console.log('Raw data:', {
                id: row.id,
                title: row.title,
                categoryId: row.categoryId,
                status: row.status,
                updatedAt: row.updatedAt,
                category_id: row.category_id,
                category_name: row.category_name,
                category_slug: row.category_slug
            });
        } else {
            console.log('❌ No data found for article 7');
            return;
        }

        // 2. Test different update scenarios
        console.log('\n2. Testing database update to category 1...');
        const updateResult = await client.query(`
            UPDATE news_articles 
            SET "categoryId" = 1, "updatedAt" = NOW() 
            WHERE id = 7 
            RETURNING id, "categoryId", "updatedAt"
        `);
        
        if (updateResult.rows.length > 0) {
            console.log('Update result:', updateResult.rows[0]);
        }

        // 3. Check after update
        console.log('\n3. Raw data after update:');
        const afterResult = await client.query(`
            SELECT 
                a.id,
                a.title,
                a."categoryId",
                c.id as category_id,
                c.name as category_name
            FROM news_articles a
            LEFT JOIN news_categories c ON a."categoryId" = c.id
            WHERE a.id = 7
        `);
        
        if (afterResult.rows.length > 0) {
            const row = afterResult.rows[0];
            console.log('After update:', {
                id: row.id,
                title: row.title,
                categoryId: row.categoryId,
                category_id: row.category_id,
                category_name: row.category_name
            });
            
            if (row.categoryId === row.category_id) {
                console.log('✅ Database level: categoryId matches category.id');
            } else {
                console.log('❌ Database level: categoryId does not match category.id');
            }
        }

        // 4. Test TypeORM-like query
        console.log('\n4. Testing TypeORM-like query structure...');
        const typeormResult = await client.query(`
            SELECT 
                a.*,
                c.id as "category.id",
                c.slug as "category.slug", 
                c.name as "category.name",
                c.description as "category.description",
                c.icon as "category.icon",
                c.color as "category.color",
                c."sortOrder" as "category.sortOrder",
                c."isActive" as "category.isActive",
                c."isPublic" as "category.isPublic",
                c."metaTitle" as "category.metaTitle",
                c."metaDescription" as "category.metaDescription",
                c."articleCount" as "category.articleCount",
                c."publishedArticleCount" as "category.publishedArticleCount",
                c."createdAt" as "category.createdAt",
                c."updatedAt" as "category.updatedAt"
            FROM news_articles a
            LEFT JOIN news_categories c ON a."categoryId" = c.id
            WHERE a.id = 7
        `);
        
        if (typeormResult.rows.length > 0) {
            const row = typeormResult.rows[0];
            console.log('TypeORM-like result:');
            console.log(`  Article categoryId: ${row.categoryId}`);
            console.log(`  Category id: ${row['category.id']}`);
            console.log(`  Category name: ${row['category.name']}`);
            
            // Check if all fields are present
            const hasAllFields = row.categoryId !== null && 
                                row['category.id'] !== null && 
                                row['category.name'] !== null;
            
            if (hasAllFields) {
                console.log('✅ All fields present in query result');
            } else {
                console.log('❌ Some fields missing in query result');
            }
        }

        console.log('\n' + '='.repeat(50));
        console.log('📋 DIAGNOSIS:');
        console.log('='.repeat(50));
        console.log('- Database level: categoryId is correctly stored and updated');
        console.log('- Join query: Works correctly');
        console.log('- Issue must be in TypeORM entity mapping or service layer');
        console.log('');
        console.log('🔧 NEXT STEPS:');
        console.log('1. Check if eager loading is interfering');
        console.log('2. Check if mapping function is handling categoryId correctly');
        console.log('3. Consider removing eager: true from entity');

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    } finally {
        await client.end();
    }
}

debugEntityQuery();
