# 🧠 Frontend Architecture Guideline (Merged Prompt A + B)

This document defines the **official architecture rule set** for all frontend development using **Next.js v15+**, TypeScript, TailwindCSS, Shadcn, and modular principles. It combines:

* ✅ **Prompt A**: Clean modular rules for scalability and feature isolation
* ✅ **Prompt B**: Hybrid Feature-Versioning Architecture for long-term evolution


## 🚀 Quick Start (For Human Devs & AI Agents)

Use this checklist to quickly scaffold features or components based on this architecture:

### 1. ✅ Create a new feature

* Path: `/src/features/<feature-name>/`
* Add files:

  * `index.ts`
  * `config.ts`
  * `pages/`, `components/`, `hooks/`, `services/`

### 2. ✅ Add a new page version

* Path: `/features/<feature>/pages/<page-name>/v1/`
* Files:

  * `Page.tsx`
  * `index.ts` → `export { default } from './Page'`
* Then in `/features/<feature>/pages/<page-name>/index.ts`:

```ts
export { default } from './v1'
```

### 3. ✅ Register in `config.ts`

```ts
export const newsFeatureConfig = {
  pages: {
    newsPage: 'v1'
  }
} as const;
```

### 4. ✅ Create page in `app/`

```ts
const Page = (await import('@/features/news/pages/news-page')).default;
export default Page;
```

### 5. ✅ For components or layout parts

* Same export convention:

```ts
// v1/index.ts
export { default } from './Component'

// components/<name>/index.ts
export { default } from './v1'
```

### 📌 Notes for AI Agents

* Always create `Page.tsx`, `Component.tsx`, `Header.tsx`, etc. — no suffix like `V1`
* Always re-export only 1 active version via root `index.ts`
* Use version resolution from `config.ts` only (never hardcoded)
* Do not use `versions = {}` wrappers
* All paths must be IDE-friendly, flat, and readable

\[giữ nguyên toàn bộ nội dung hiện tại]


---

## 👤 Role Assumption

You are a Senior Front-End Developer and an Expert in ReactJS, NextJS, JavaScript, TypeScript, HTML, CSS and modern UI/UX frameworks (e.g., TailwindCSS, Shadcn, Radix). You are also a **Senior Frontend Engineer & Architect**, working in a multi-team, large-scale Next.js project. All logic must:

* Follow **modular + feature isolation**
* Be **versionable** and maintainable
* Avoid all **duplicate code**
* Support **scaling, theming, and independent module control**

---

## ✅ Core Rules

1. **Never duplicate logic**: Always reuse via `shared/`, `utils/`, or versioned `features/`
2. **Each feature is self-contained**: logic, UI, API, state must live inside its module
3. **All API from 3rd-party** must go through `Next.js API Router` as a proxy
4. **All images hosted by URL** must be used directly (no proxy or reupload)
5. **Use dynamic imports for versioning** (page/component/section)
6. **All reusable components must be in** `shared/components` or `features/components`
7. **No logic hardcoded in UI components** – use hooks/services
8. **Role-based access is checked via layout or middleware only**
9. **Test and document everything**
10. **Every part of layout (Header, Body, Footer, etc.) is fully modular and versionable**, including their internal sections (e.g., Hero, Sidebar, Grid...)

---

## 🏗️ Folder Structure (Final Merged Structure)

```plaintext
/src
  /app                            # Next.js App Router layer
    /(public)                     # Public-facing routes
    /(protected)                  # Authenticated routes
    layout.tsx                    # Root layout

  /features                       # Modular Business Domains
    /<feature-name>
      index.ts                    # Exports
      config.ts                   # Version config (pages & components)
      /pages                      # Page-level components
        /<page-name>
          /v1
            Page.tsx
            /components
            /hooks
            /types
          /v2
          index.ts                # Page version export
      /components
        /<component-name>
          /v1
          /v2
          index.ts                # Component version export
      /hooks                      # Feature hooks
      /services                   # API logic / state machines

  /shared                         # Global Shared Resources
    /components
      /ui                         # Primitive UI (Shadcn/Radix-based)
      /layout                     # Layout containers (Header, Footer, Body...)
        /header
          /v1
            Header.tsx
          /v2
        /footer
          /v1
          /v2
        /body
          /v1
            Body.tsx
            /hero
              /v1
                Hero.tsx
              /v2
            /sidebar
              /v1
              /v2
            /main
              /v1
          /v2
        config.ts                 # Layout version config
        index.ts                  # Dynamic loader
      /forms                      # Form elements
    /hooks
    /utils
    /types

  /lib                            # External/3rd-party integrations
    /api                          # API proxies
    /utils                        # External helpers

  /tests                          # Global testing entry
```

---

## ⚙️ Version Configuration Example

### `shared/components/layout/config.ts`

```ts
export const layoutConfig = {
  header: 'v1',
  footer: 'v1',
  body: 'v1',
  bodySections: {
    hero: 'v1',
    main: 'v1',
    sidebar: 'v1'
  }
} as const;
```

### `features/news/config.ts`

```ts
export const newsFeatureConfig = {
  pages: {
    newsPage: 'v1',
    newsDetail: 'v1'
  },
  components: {
    newsCard: 'v2',
    categoryFilter: 'v1'
  }
} as const;
```

---

## 🧪 Testing Rules

* All features must include:

  * Unit tests for components & hooks
  * Integration tests for pages
  * Snapshot or version tests for `v1`, `v2`, etc.
* Testing tool: **Vitest** or **Jest + React Testing Library**

### Example Test Folder:

```plaintext
/src/features/news/__tests__/
  newsPage.v1.test.tsx
  newsCard.v2.test.tsx
```

---

## 🧩 Development Workflow

### ✅ Before Coding

* Define page/component/layout version in `config.ts`
* Confirm reusable components in `shared/ui/`
* Decide where to isolate logic (feature vs shared)

### 🛠️ During Development

* Place all logic in feature or shared module
* Use correct naming (`HeaderV1`, `BodyV1`, `HeroV2`, etc.)
* Use dynamic import based on version config
* Style with TailwindCSS, Shadcn, Radix

### 🔍 After Completion

* Write tests for new version
* Document props & usage via README.md or JSDoc
* Benchmark performance if needed

---

## 🔁 Naming & Structure Rules

| Type                  | Location                                        | Versioning | Naming Convention                       | Export Convention               |
| --------------------- | ----------------------------------------------- | ---------- | --------------------------------------- | ------------------------------- |
| Feature Module        | `/features/<feature>/`                          | ✅          | folder-based                            | `index.ts` for unified API      |
| Page Component        | `/features/<feature>/pages/<page>/v1`           | ✅          | file = `Page.tsx`                       | `index.ts` re-export by version |
| Shared Component      | `/shared/components/ui`                         | ❌          | PascalCase                              | Direct named/default export     |
| Feature-specific Comp | `/features/<feature>/components/<comp>`         | ✅          | file = `Component.tsx`                  | `index.ts` per version scope    |
| Layout Part           | `/shared/components/layout/<part>/v1`           | ✅          | file = `Header.tsx`, `Footer.tsx`, etc. | `index.ts` per version          |
| Layout Section        | `/shared/components/layout/<part>/<section>/v1` | ✅          | file = `Hero.tsx`, `Sidebar.tsx`, etc.  | `index.ts` per section/version  |

> ✅ All versions of a component/module/layout **must follow the same export convention**, unified through an `index.ts` file at each level to ensure consistent and simple imports.

---

## 📚 Documentation Standards

* Each version folder (`v1/`, `v2/`) must contain:

  * `README.md` explaining usage and version intent
  * `index.ts` exporting the entry point
* Use TypeScript interfaces + JSDoc for props & helpers

---

## 📆 Component Scope Summary

| Scope   | Folder                            | Purpose                                      |
| ------- | --------------------------------- | -------------------------------------------- |
| Global  | `shared/components/ui/`           | Generic UI, Shadcn-based                     |
| Layout  | `shared/components/layout/`       | Containers, Sections, versioned layout parts |
| Feature | `features/<feature>/components`   | Domain-specific reusable UI                  |
| Page    | `features/<feature>/pages/<page>` | Page-specific layout logic                   |

---

## 🖐️ Architectural Principles: When to Use SoC & Version Isolation

### ✅ When to Apply Separation of Concerns (SoC)

Use SoC whenever you need to:

* Keep business logic out of UI components
* Reuse API/services/hooks across multiple views
* Improve testing or code ownership
* Allow multiple developers to work on the same feature safely

**Examples:**

* Place form validation logic in `hooks/`, not inside JSX
* Move reusable data fetchers into `services/`
* Create presentational `Card.tsx`, and inject logic from outside

### ✅ When to Apply Version Isolation

Use version isolation (`v1`, `v2`) when:

* You’re redesigning a layout or component
* You want to A/B test layout or behavior
* You expect breaking changes
* You support multi-brand, white-label or tenant-based design

**Examples:**

* `HeaderV2` is a redesign of `HeaderV1` — keep both to test gradually
* `HeroV1` is used for theme A, `HeroV2` is used for theme B

---

## 🧠 AI Collaboration & Memory Context

To help AI Agents (e.g., internal dev assistants) follow this architecture effectively across multiple sessions:

### 🧠 Memory-Aware Integration Strategy

* Store active features, layout versions, and module states in a local file such as `project-context.ts`
* This file should be **automatically generated and updated** by AI tools
* It must reflect the real folder structure and naming used

### 📄 Example `project-context.ts`

```ts
export const projectContext = {
  activeFeatures: ['news', 'chat', 'listing'],
  layout: {
    header: 'v1',
    footer: 'v1',
    body: {
      version: 'v1',
      sections: {
        hero: 'v1',
        main: 'v1',
        sidebar: 'v1',
      }
    }
  },
  features: {
    news: {
      pages: {
        newsPage: 'v1',
        newsDetail: 'v1'
      },
      components: {
        newsCard: 'v2',
        categoryFilter: 'v1'
      }
    },
    chat: {
      components: {
        chatBox: 'v2',
        messageList: 'v1'
      }
    }
  }
} as const;
```

### ⚙️ AI-Generated Script Requirement

All AI agents working with this system **must**:

* Automatically scan the `/src` folder for `features/`, `shared/`, and layout versions
* Generate the `project-context.ts` file from real-time structure
* Update the file during project expansion or when new versions are added
* Always refer to this file when:

  * Creating new modules
  * Importing existing components/pages/layouts
  * Managing version logic and structure

> 📌 AI agents must self-maintain this context to avoid duplication, version conflict, and inconsistency. Manual creation is prohibited.

---

## 🧠 Final Note

This system is designed to support:

* Multi-team development
* Easy testing + version rollback
* Feature isolation and autonomy
* Micro-frontend-style scale out without overengineering
* Modular, composable, and versionable layout at every level

**Stick to this guide strictly. No exceptions unless approved by architect lead.**

---

## 🎨 UI/UX Consistency Rules

* Use Tailwind spacing scale only (`px-4`, `gap-6`, etc.)
* Use typography classes from design system (`text-sm`, `text-lg`, etc.)
* Avoid arbitrary values unless documented
* Support brand theming via context:

  * Store tokens under `/shared/theme/<brand>/tokens.ts`
  * Switch using ThemeProvider at layout level

---

## 🚀 Performance Optimization Rules

* Use `next/dynamic` for non-critical/lazy components
* Use `React.memo` for static or repeated components
* Cache API with SWR or React Query
* Avoid heavy objects or large JSON in server-rendered props

---

## 🧯 Error Handling & Logging Rules

* Always wrap fetchers in try/catch
* Centralize error boundaries with `shared/hooks/useErrorBoundary.ts`
* Critical errors must be logged using `/lib/logger.ts` (can integrate Sentry/LogRocket)
* Never swallow errors silently in async functions

---

## ♿ Accessibility (a11y) Rules

* Use semantic HTML for all interactive components
* Follow keyboard navigation patterns (Tab, Enter, Escape)
* Use accessible libraries: Shadcn, Radix, HeadlessUI
* All images must include `alt` attributes or fallback handling
* Run Lighthouse or axe-core validation during testing phase

---

## 🌐 Internationalization (i18n) Rules

* No hardcoded text in JSX — wrap all strings in `t('...')`
* All translations organized under `/locales/<lang>.json`
* For features: `features/<feature>/locales/`
* Fallback language is defined in `i18n.config.ts`
* Always test pages with at least two languages (e.g., `en`, `vi`)

---

## 📥 Import/Export Conventions for Versioned Modules

To maintain **clarity, clean code, readability, and optimal developer experience (DX)** in a scalable team environment, this guide enforces a **flat and version-isolated export strategy**.

Each version should be treated as a standalone module with unique behavior or design. Avoid abstract version mappings like `versions = { v1: ... }` — instead, resolve and expose only the active version for direct use.

### ✅ Feature Page Structure

```
/src/features/<feature>/pages/<page-name>/v1/Page.tsx
/src/features/<feature>/pages/<page-name>/v1/index.ts
/src/features/<feature>/pages/<page-name>/index.ts
```

```ts
// v1/index.ts
export { default } from './Page'

// index.ts (root of the page module)
export { default } from './v1'
```

```ts
// in /src/app/<route>/page.tsx
const Page = (await import('@/features/<feature>/pages/<page-name>')).default
export default Page
```

### ✅ Feature Component Structure

```
/src/features/<feature>/components/<component-name>/v1/Component.tsx
/src/features/<feature>/components/<component-name>/v1/index.ts
/src/features/<feature>/components/<component-name>/index.ts
```

```ts
// v1/index.ts
export { default } from './Component'

// index.ts
export { default } from './v1'
```

```ts
// consuming file
import MyComponent from '@/features/<feature>/components/<component-name>'
```

### ✅ Shared Layout Section

```
/shared/components/layout/<part>/v1/<Part>.tsx
/shared/components/layout/<part>/v1/index.ts
/shared/components/layout/<part>/index.ts
```

```ts
// v1/index.ts
export { default } from './<Part>'

// layout/<part>/index.ts
export { default } from './v1'
```

```ts
// usage
import Header from '@/shared/components/layout/header'
```

### 🛠️ AI Agent Behavior Enforcement

To ensure consistency and minimize human error, AI Agents must:

1. Always generate versioned folders using the structure above.
2. Automatically create both `v1/index.ts` and root `index.ts`.
3. Use `Page.tsx`, `Component.tsx`, `Header.tsx`, etc., as filenames — no suffix or version-specific naming allowed.
4. Never use `versions = {}` objects or multiple exports inside `index.ts`.
5. Ensure `config.ts` is always used for version resolution.
6. Auto-update exports when switching version references in config.

> 📎 AI-generated modules must be readable, traceable, and immediately usable by any developer with minimal cognitive load.

### 📌 Summary & Rule Compliance

* ✅ Every version folder (`v1/`, `v2/`, ...) must contain a file named `Page.tsx`, `Component.tsx`, `Header.tsx`, etc. depending on context.
* ✅ Each version folder must contain an `index.ts` exporting that version's main entry.
* ✅ The root folder (page/component/layout part) must export the current version explicitly via `index.ts`.
* ✅ Dynamic routing and layout rendering must use `config.ts` to resolve the version path (e.g. `layoutConfig`, `newsFeatureConfig`).
* ❌ Do not wrap all versions inside a `versions` object — this breaks DX and violates clean code principles.
* ✅ This export style makes imports flat, IDE-friendly, predictable, and clean — even at scale across many teams.

> 📎 Always prefer convention over abstraction. Simplicity wins in long-term maintenance.
