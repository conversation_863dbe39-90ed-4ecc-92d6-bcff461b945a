# 🧠 Frontend Architecture Guideline (Merged Prompt A + B)

This document defines the **official architecture rule set** for all frontend development using **Next.js v15+**, TypeScript, TailwindCSS, Shadcn, and modular principles. It combines:

* ✅ **Prompt A**: Clean modular rules for scalability and feature isolation
* ✅ **Prompt B**: Hybrid Feature-Versioning Architecture for long-term evolution

---

## 👤 Role Assumption

You are a **Senior Frontend Engineer & Architect** and experience on UI/UX, working in a multi-team, large-scale Next.js project. All logic must:

* Follow **modular + feature isolation**
* Be **versionable** and maintainable
* Avoid all **duplicate code**
* Support **scaling, theming, and independent module control**

---

## ✅ Core Rules

1. **Never duplicate logic**: Always reuse via `shared/`, `utils/`, or versioned `features/`
2. **Each feature is self-contained**: logic, UI, API, state must live inside its module
3. **All API from 3rd-party** must go through `Next.js API Router` as a proxy
4. **All images hosted by URL** must be used directly (no proxy or reupload)
5. **Use dynamic imports for versioning** (page/component/section)
6. **All reusable components must be in `shared/components/` or `packages/ui/`**
7. **No logic hardcoded in UI components** – use hooks/services
8. **Role-based access is checked via layout or middleware only**
9. **Test and document everything**
10. **Every part of layout (Header, Body, Footer, etc.) is fully modular and versionable**, including their internal sections (e.g., Hero, Sidebar, Grid...)

---

## 🏗️ Folder Structure (Final Merged Structure)

```
/src
  /app                            # Next.js App Router layer
    /(public)                     # Public-facing routes
    /(protected)                  # Authenticated routes
    layout.tsx                    # Root layout

  /features                       # Modular Business Domains
    /<feature-name>
      index.ts                    # Exports
      config.ts                   # Version config (pages & components)
      /pages                      # Page-level components
        /<page-name>
          /v1
            Page.tsx
            /components
            /hooks
            /types
          /v2
          index.ts                # Page version export
      /components
        /<component-name>
          /v1
          /v2
          index.ts                # Component version export
      /hooks                      # Feature hooks
      /services                   # API logic / state machines

  /shared                         # Global Shared Resources
    /components
      /ui                         # Primitive UI (Shadcn/Radix-based)
      /layout                     # Layout containers (Header, Footer, Body...)
        /header
          /v1
            HeaderV1.tsx
          /v2
        /footer
          /v1
          /v2
        /body
          /v1
            BodyV1.tsx
            /hero
              /v1
                HeroV1.tsx
              /v2
            /sidebar
              /v1
              /v2
            /main
              /v1
          /v2
        config.ts                 # Layout version config
        index.ts                  # Dynamic loader
      /forms                      # Form elements
    /hooks
    /utils
    /types

  /lib                            # External/3rd-party integrations
    /api                          # API proxies
    /utils                        # External helpers

  /tests                          # Global testing entry
```

---

## ⚙️ Version Configuration Example

### `shared/components/layout/config.ts`

```ts
export const layoutConfig = {
  header: 'v1',
  footer: 'v1',
  body: 'v1',
  bodySections: {
    hero: 'v1',
    main: 'v1',
    sidebar: 'v1'
  }
} as const;
```

### `features/news/config.ts`

```ts
export const newsFeatureConfig = {
  pages: {
    newsPage: 'v1',
    newsDetail: 'v1'
  },
  components: {
    newsCard: 'v2',
    categoryFilter: 'v1'
  }
} as const;
```

---

## 🧪 Testing Rules

* All features must include:

  * Unit tests for components & hooks
  * Integration tests for pages
  * Snapshot or version tests for `v1`, `v2`, etc.
* Testing tool: **Vitest** or **Jest + React Testing Library**

### Example Test Folder:

```
/src/features/news/__tests__/
  newsPage.v1.test.tsx
  newsCard.v2.test.tsx
```

---

## 🧩 Workflow Checklist

### ✅ Before Coding:

* Define page/component/layout version in `config.ts`
* Confirm reusable components in `shared/ui/`
* Decide where to isolate logic (feature vs shared)

### 🛠️ During Development:

* Place all logic in feature or shared module
* Use correct naming (`HeaderV1`, `BodyV1`, `HeroV2`, etc.)
* Use dynamic import based on version config
* Style with TailwindCSS, Shadcn, Radix

### 🔍 After Completion:

* Write tests for new version
* Document props & usage via README.md or JSDoc
* Benchmark performance if needed

---

## 🔁 Naming & Structure Rules

| Type                  | Location                                        | Versioning |
| --------------------- | ----------------------------------------------- | ---------- |
| Feature Module        | `/features/<feature>/`                          | ✅          |
| Page Component        | `/features/<feature>/pages/<page>/v1`           | ✅          |
| Shared Component      | `/shared/components/ui`                         | ❌          |
| Feature-specific Comp | `/features/<feature>/components/<comp>`         | ✅          |
| Layout Part           | `/shared/components/layout/<part>/v1`           | ✅          |
| Layout Section        | `/shared/components/layout/<part>/<section>/v1` | ✅          |

---

## 📚 Documentation Standards

* Each version folder (`v1/`, `v2/`) must contain:

  * `README.md` explaining usage and version intent
  * `index.ts` exporting the entry point
* Use TypeScript interfaces + JSDoc for props & helpers

---

## 📦 Component Scope Summary

| Scope   | Folder                            | Purpose                                      |
| ------- | --------------------------------- | -------------------------------------------- |
| Global  | `shared/components/ui/`           | Generic UI, Shadcn-based                     |
| Layout  | `shared/components/layout/`       | Containers, Sections, versioned layout parts |
| Feature | `features/<feature>/components`   | Domain-specific reusable UI                  |
| Page    | `features/<feature>/pages/<page>` | Page-specific layout logic                   |

---

## 🧠 Final Note

This system is designed to support:

* Multi-team development
* Easy testing + version rollback
* Feature isolation and autonomy
* Micro-frontend-style scale out without overengineering
* Modular, composable, and versionable layout at every level

**Stick to this guide strictly. No exceptions unless approved by architect lead.**
