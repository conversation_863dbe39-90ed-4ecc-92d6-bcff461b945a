const axios = require('axios');

async function testExistingArticle() {
    try {
        console.log('🎯 Testing CategoryId Update with Existing Article');
        console.log('=' * 50);
        
        // 1. Login
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        const token = loginResponse.data.accessToken;
        console.log('✅ Login successful');
        
        // 2. Get existing articles
        console.log('\n2. Getting existing articles...');
        const articlesResponse = await axios.get('http://localhost:3000/admin/news/articles', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log(`📰 Found ${articlesResponse.data.meta.totalItems} articles:`);
        articlesResponse.data.data.forEach(article => {
            console.log(`  - ID: ${article.id}, Title: "${article.title}"`);
            console.log(`    CategoryId: ${article.categoryId}, Category: ${article.category?.name}`);
        });
        
        if (articlesResponse.data.data.length === 0) {
            console.log('❌ No articles found to test');
            return;
        }
        
        const testArticle = articlesResponse.data.data[0];
        console.log(`\n🎯 Testing with Article ID: ${testArticle.id}`);
        console.log(`Current CategoryId: ${testArticle.categoryId}`);
        console.log(`Current Category: ${testArticle.category?.name}`);
        
        // 3. Get categories
        console.log('\n3. Getting categories...');
        const categoriesResponse = await axios.get('http://localhost:3000/admin/news/categories', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log(`🗂️ Available categories:`);
        categoriesResponse.data.data.forEach(category => {
            console.log(`  - ID: ${category.id}, Name: "${category.name}"`);
        });
        
        // 4. Test categoryId update to different category
        const targetCategoryId = categoriesResponse.data.data.find(cat => cat.id !== testArticle.categoryId)?.id;
        
        if (!targetCategoryId) {
            console.log('❌ No different category found to test with');
            return;
        }
        
        console.log(`\n4. Updating categoryId from ${testArticle.categoryId} to ${targetCategoryId}...`);
        
        const updateResponse = await axios.patch(
            `http://localhost:3000/admin/news/articles/${testArticle.id}`,
            { categoryId: targetCategoryId },
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ Update request successful!');
        console.log('Response data:');
        console.log(`  Article ID: ${updateResponse.data.id}`);
        console.log(`  Title: "${updateResponse.data.title}"`);
        console.log(`  CategoryId: ${updateResponse.data.categoryId}`);
        console.log(`  Category Name: ${updateResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${updateResponse.data.category?.id}`);
        console.log(`  Updated At: ${updateResponse.data.updatedAt}`);
        
        // 5. Verify the update
        console.log('\n5. Verifying update by fetching article again...');
        const verifyResponse = await axios.get(
            `http://localhost:3000/admin/news/articles/${testArticle.id}`,
            {
                headers: { 'Authorization': `Bearer ${token}` }
            }
        );
        
        console.log('Verification result:');
        console.log(`  CategoryId: ${verifyResponse.data.categoryId}`);
        console.log(`  Category Name: ${verifyResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${verifyResponse.data.category?.id}`);
        
        // 6. Final validation
        const updateSuccess = updateResponse.data.categoryId === targetCategoryId;
        const verifySuccess = verifyResponse.data.categoryId === targetCategoryId;
        const categoryObjectSuccess = verifyResponse.data.category?.id === targetCategoryId;
        
        console.log('\n' + '='.repeat(50));
        console.log('🎯 FINAL RESULTS:');
        console.log('='.repeat(50));
        
        if (updateSuccess && verifySuccess && categoryObjectSuccess) {
            console.log('🎉 PERFECT SUCCESS! CategoryId update is working 100%!');
            console.log('✅ Update response has correct categoryId');
            console.log('✅ Verification response has correct categoryId');
            console.log('✅ Category object is correctly populated');
            console.log('✅ Database update is working');
            console.log('✅ Response mapping is working');
            
            console.log('\n🎯 YOUR COMMAND WORKS NOW:');
            console.log(`curl -X PATCH "http://localhost:3000/admin/news/articles/${testArticle.id}" \\`);
            console.log(`  -H "Content-Type: application/json" \\`);
            console.log(`  -H "Authorization: Bearer ${token}" \\`);
            console.log(`  -d '{"categoryId":${testArticle.categoryId}}'`);
            
        } else {
            console.log('❌ STILL HAS ISSUES:');
            console.log(`  Update response categoryId: ${updateResponse.data.categoryId} (expected: ${targetCategoryId}) - ${updateSuccess ? '✅' : '❌'}`);
            console.log(`  Verify response categoryId: ${verifyResponse.data.categoryId} (expected: ${targetCategoryId}) - ${verifySuccess ? '✅' : '❌'}`);
            console.log(`  Category object ID: ${verifyResponse.data.category?.id} (expected: ${targetCategoryId}) - ${categoryObjectSuccess ? '✅' : '❌'}`);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

testExistingArticle();
