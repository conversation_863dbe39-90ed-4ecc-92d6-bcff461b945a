const axios = require('axios');

async function testCategoryIdFix() {
    try {
        console.log('🔍 Testing CategoryId Fix...');
        
        // 1. Login
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        const token = loginResponse.data.accessToken;
        console.log('✅ Login successful');
        
        // 2. Get article 7 before update
        console.log('\n2. Getting article 7 before update...');
        const beforeResponse = await axios.get('http://localhost:3000/admin/news/articles/7', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log('Before update:');
        console.log(`  Title: "${beforeResponse.data.title}"`);
        console.log(`  CategoryId: ${beforeResponse.data.categoryId}`);
        console.log(`  Category Name: ${beforeResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${beforeResponse.data.category?.id}`);
        
        // 3. Update categoryId to 1
        console.log('\n3. Updating categoryId to 1...');
        const updateResponse = await axios.patch(
            'http://localhost:3000/admin/news/articles/7',
            { categoryId: 1 },
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('Update response:');
        console.log(`  Title: "${updateResponse.data.title}"`);
        console.log(`  CategoryId: ${updateResponse.data.categoryId}`);
        console.log(`  Category Name: ${updateResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${updateResponse.data.category?.id}`);
        console.log(`  Updated At: ${updateResponse.data.updatedAt}`);
        
        // 4. Get article again to verify
        console.log('\n4. Getting article 7 after update...');
        const afterResponse = await axios.get('http://localhost:3000/admin/news/articles/7', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log('After update (fresh fetch):');
        console.log(`  Title: "${afterResponse.data.title}"`);
        console.log(`  CategoryId: ${afterResponse.data.categoryId}`);
        console.log(`  Category Name: ${afterResponse.data.category?.name}`);
        console.log(`  Category ID from object: ${afterResponse.data.category?.id}`);
        
        // 5. Check if categoryId matches category.id
        if (afterResponse.data.categoryId === afterResponse.data.category?.id) {
            console.log('\n✅ SUCCESS: categoryId matches category.id');
        } else {
            console.log('\n❌ MISMATCH: categoryId does not match category.id');
            console.log(`  categoryId: ${afterResponse.data.categoryId}`);
            console.log(`  category.id: ${afterResponse.data.category?.id}`);
        }
        
        // 6. Test with category 2
        console.log('\n6. Testing update to category 2...');
        const update2Response = await axios.patch(
            'http://localhost:3000/admin/news/articles/7',
            { categoryId: 2 },
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('Update to category 2:');
        console.log(`  CategoryId: ${update2Response.data.categoryId}`);
        console.log(`  Category Name: ${update2Response.data.category?.name}`);
        console.log(`  Category ID from object: ${update2Response.data.category?.id}`);
        
        if (update2Response.data.categoryId === 2 && update2Response.data.category?.id === 2) {
            console.log('\n🎉 PERFECT! CategoryId update is working correctly!');
        } else {
            console.log('\n❌ Still has issues with categoryId update');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

testCategoryIdFix();
