const axios = require('axios');

async function testYourCase() {
    try {
        console.log('🔍 Testing your specific case...');
        
        // 1. Get fresh token
        console.log('1. Getting fresh token...');
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        const token = loginResponse.data.accessToken;
        console.log('✅ Fresh token obtained');
        
        // 2. Check what ID 6 is (article or category)
        console.log('\n2. Checking what ID 6 is...');
        
        // Check if ID 6 is an article
        try {
            const articleResponse = await axios.get('http://localhost:3000/admin/news/articles/6', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            console.log('✅ ID 6 is an ARTICLE:');
            console.log(`   Title: "${articleResponse.data.title}"`);
            console.log(`   Current Category ID: ${articleResponse.data.categoryId}`);
            
            // Test your original intent: update article's categoryId
            console.log('\n3. Testing article categoryId update...');
            const updateResponse = await axios.patch(
                'http://localhost:3000/admin/news/articles/6',
                { categoryId: 1 },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            console.log('✅ Article categoryId updated successfully!');
            console.log(`   New Category ID: ${updateResponse.data.categoryId}`);
            console.log(`   Category Name: ${updateResponse.data.category.name}`);
            
        } catch (articleError) {
            if (articleError.response?.status === 404) {
                console.log('❌ ID 6 is not an article');
                
                // Check if ID 6 is a category
                try {
                    const categoryResponse = await axios.get('http://localhost:3000/admin/news/categories/6', {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    console.log('✅ ID 6 is a CATEGORY:');
                    console.log(`   Name: "${categoryResponse.data.name}"`);
                    console.log(`   Slug: "${categoryResponse.data.slug}"`);
                    
                    console.log('\n❌ Cannot set categoryId on a category!');
                    console.log('   Categories don\'t have categoryId field.');
                    console.log('   You can update: name, description, color, sortOrder, etc.');
                    
                    // Show correct category update
                    console.log('\n3. Testing correct category update...');
                    const updateResponse = await axios.patch(
                        'http://localhost:3000/admin/news/categories/6',
                        { 
                            name: `${categoryResponse.data.name} - Updated`,
                            description: 'Updated via test script'
                        },
                        {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                    
                    console.log('✅ Category updated successfully!');
                    console.log(`   New Name: "${updateResponse.data.name}"`);
                    
                } catch (categoryError) {
                    if (categoryError.response?.status === 404) {
                        console.log('❌ ID 6 does not exist as article or category');
                        
                        // Show available IDs
                        console.log('\n4. Showing available IDs...');
                        
                        const articlesResponse = await axios.get('http://localhost:3000/admin/news/articles', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });
                        
                        const categoriesResponse = await axios.get('http://localhost:3000/admin/news/categories', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });
                        
                        console.log('\n📰 Available Articles:');
                        articlesResponse.data.data.forEach(article => {
                            console.log(`   ID: ${article.id}, Title: "${article.title}"`);
                        });
                        
                        console.log('\n🗂️ Available Categories:');
                        categoriesResponse.data.data.forEach(category => {
                            console.log(`   ID: ${category.id}, Name: "${category.name}"`);
                        });
                        
                    } else {
                        throw categoryError;
                    }
                }
            } else {
                throw articleError;
            }
        }
        
        console.log('\n' + '='.repeat(50));
        console.log('📋 SUMMARY OF YOUR ISSUES:');
        console.log('='.repeat(50));
        console.log('❌ Wrong port: 4000 → should be 3000');
        console.log('❌ Wrong endpoint: /api/news/6 → should be /admin/news/articles/6 or /admin/news/categories/6');
        console.log('❌ Possibly expired token → get fresh token');
        console.log('❌ Check if ID 6 exists');
        console.log('❌ Check if you\'re updating the right entity type');
        
        console.log('\n✅ CORRECT EXAMPLES:');
        console.log('Update article categoryId:');
        console.log('  PATCH http://localhost:3000/admin/news/articles/6');
        console.log('  {"categoryId": 1}');
        console.log('');
        console.log('Update category name:');
        console.log('  PATCH http://localhost:3000/admin/news/categories/6');
        console.log('  {"name": "New Category Name"}');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

testYourCase();
