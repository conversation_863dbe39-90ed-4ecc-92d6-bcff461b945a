const axios = require('axios');

async function debugCategoryUpdate() {
    console.log('🔍 DEBUGGING CATEGORY UPDATE ISSUES');
    console.log('=' * 50);

    try {
        // Test 1: Check server connectivity - skip this test
        console.log('\n1. Skipping connectivity test, going directly to login...');

        // Test 2: Login and token format
        console.log('\n2. Testing login and token format...');
        const loginResponse = await axios.post('http://localhost:3000/system-auth/login', {
            username: 'admin',
            password: 'admin123456'
        });

        console.log('Login response keys:', Object.keys(loginResponse.data));
        console.log('✅ Login successful');

        // Check both possible token field names
        const token = loginResponse.data.accessToken || loginResponse.data.access_token;
        if (!token) {
            console.log('❌ No token found in response');
            console.log('Response data:', loginResponse.data);
            return;
        }

        console.log('✅ Token found:', token.substring(0, 50) + '...');

        // Test 3: Check authentication with token
        console.log('\n3. Testing authentication...');
        try {
            const authTest = await axios.get('http://localhost:3000/admin/news/categories', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            console.log('✅ Authentication successful');
            console.log(`Found ${authTest.data.meta.totalItems} categories`);
        } catch (error) {
            console.log('❌ Authentication failed:', error.response?.status, error.response?.data);
            return;
        }

        // Test 4: Check if categories exist
        console.log('\n4. Checking available categories...');
        const categoriesResponse = await axios.get('http://localhost:3000/admin/news/categories', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (categoriesResponse.data.data.length === 0) {
            console.log('❌ No categories found to update');
            console.log('You need to create categories first');
            return;
        }

        const categories = categoriesResponse.data.data;
        console.log('✅ Available categories:');
        categories.forEach(cat => {
            console.log(`  - ID: ${cat.id}, Name: "${cat.name}", Slug: "${cat.slug}"`);
        });

        // Test 5: Test different update scenarios
        console.log('\n5. Testing different update scenarios...');
        const testCategory = categories[0];

        // Scenario A: Minimal update
        console.log('\n5a. Testing minimal update (name only)...');
        try {
            const minimalUpdate = await axios.patch(
                `http://localhost:3000/admin/news/categories/${testCategory.id}`,
                { name: `${testCategory.name} - Minimal Update` },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('✅ Minimal update successful');
        } catch (error) {
            console.log('❌ Minimal update failed:', error.response?.status, error.response?.data);
        }

        // Scenario B: Full update
        console.log('\n5b. Testing full update...');
        try {
            const fullUpdate = await axios.patch(
                `http://localhost:3000/admin/news/categories/${testCategory.id}`,
                {
                    name: `${testCategory.name} - Full Update`,
                    description: 'Full update test description',
                    color: '#4ECDC4',
                    sortOrder: 5,
                    isActive: true,
                    isPublic: true,
                    metaTitle: 'Test Meta Title',
                    metaDescription: 'Test meta description'
                },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('✅ Full update successful');
        } catch (error) {
            console.log('❌ Full update failed:', error.response?.status, error.response?.data);
        }

        // Test 6: Test invalid scenarios
        console.log('\n6. Testing error scenarios...');

        // Invalid category ID
        console.log('\n6a. Testing invalid category ID...');
        try {
            await axios.patch(
                'http://localhost:3000/admin/news/categories/99999',
                { name: 'Test' },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('❌ Should have failed with invalid ID');
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('✅ Correctly returned 404 for invalid ID');
            } else {
                console.log('❌ Unexpected error for invalid ID:', error.response?.status, error.response?.data);
            }
        }

        // Invalid data
        console.log('\n6b. Testing invalid data...');
        try {
            await axios.patch(
                `http://localhost:3000/admin/news/categories/${testCategory.id}`,
                { color: 'invalid-color' }, // Invalid hex color
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('❌ Should have failed with invalid color');
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('✅ Correctly returned 400 for invalid data');
            } else {
                console.log('❌ Unexpected error for invalid data:', error.response?.status, error.response?.data);
            }
        }

        console.log('\n' + '=' * 50);
        console.log('🎉 DEBUG COMPLETE - Category update is working!');
        console.log('=' * 50);

        console.log('\n📋 COMMON ISSUES & SOLUTIONS:');
        console.log('1. Token field: Use "accessToken" not "access_token"');
        console.log('2. Authorization header: "Bearer YOUR_TOKEN"');
        console.log('3. Content-Type: "application/json"');
        console.log('4. Check category ID exists');
        console.log('5. Validate data format (hex colors, etc.)');

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

debugCategoryUpdate();
