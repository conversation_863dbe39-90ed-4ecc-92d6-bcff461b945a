import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmOptionsFactory, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class DatabaseService implements TypeOrmOptionsFactory, OnModuleInit {
    private readonly logger = new Logger(DatabaseService.name);

    constructor(
        private configService: ConfigService,
    ) { }

    createTypeOrmOptions(): TypeOrmModuleOptions {
        return {
            type: 'postgres',
            host: this.configService.get<string>('app.dbHost', 'localhost'),
            port: this.configService.get<number>('app.dbPort', 5432),
            username: this.configService.get<string>('app.dbUser'),
            password: this.configService.get<string>('app.dbPassword'),
            database: this.configService.get<string>('app.dbName'),
            entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
            synchronize: false, // ⚠️ TEMPORARILY DISABLED - will enable with proper migration strategy
            // migrations: [__dirname + '/../../database/migrations/*{.ts,.js}'],
            // migrationsRun: true, // Auto-run migrations on startup
            logging: ['error', 'schema'],
            maxQueryExecutionTime: 1000,
            poolSize: 5, // Giới hạn số kết nối
            extra: {
                max: 5, // Tối đa 5 kết nối
                idleTimeoutMillis: 30000,
            },
        };
    }

    /**
     * Check database timezone configuration on module initialization
     */
    async onModuleInit() {
        try {
            // Create a temporary connection to check timezone
            const dataSource = new DataSource({
                type: 'postgres',
                host: this.configService.get<string>('app.dbHost', 'localhost'),
                port: this.configService.get<number>('app.dbPort', 5432),
                username: this.configService.get<string>('app.dbUser'),
                password: this.configService.get<string>('app.dbPassword'),
                database: this.configService.get<string>('app.dbName'),
            });

            await dataSource.initialize();

            // Check database timezone
            const result = await dataSource.query('SHOW timezone');

            // Extract timezone (PostgreSQL returns 'TimeZone' field)
            const dbTimezone = result[0]?.timezone || result[0]?.TimeZone || result[0]?.TIMEZONE;

            await dataSource.destroy();

            // Log timezone information
            this.logger.log(`Database timezone: ${dbTimezone}`);

            // Check if timezone is UTC-compatible
            const isUtcCompatible = dbTimezone === 'UTC' || dbTimezone === 'Etc/UTC' || dbTimezone === 'GMT';

            if (!isUtcCompatible) {
                this.logger.warn(`⚠️  Database timezone is '${dbTimezone}', recommended: 'UTC'`);
                this.logger.warn(`   To fix: ALTER DATABASE ${this.configService.get('app.dbName')} SET timezone TO 'UTC';`);
            } else {
                this.logger.log('✅ Database timezone is correctly set to UTC');
            }

            // Check application timezone
            const appTz = process.env.TZ;
            if (appTz !== 'UTC') {
                this.logger.warn(`⚠️  Application TZ is '${appTz || 'system default'}', recommended: 'UTC'`);
                this.logger.warn(`   To fix: Set TZ=UTC in environment variables`);
            } else {
                this.logger.log('✅ Application timezone is correctly set to UTC');
            }

        } catch (error) {
            this.logger.error(`Failed to check database timezone: ${error.message}`);
        }
    }
}