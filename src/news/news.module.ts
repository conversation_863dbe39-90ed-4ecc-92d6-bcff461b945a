import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Category entities, services, and controllers
import { NewsCategory } from './categories/entities/news-category.entity';
import { NewsCategoryService } from './categories/services/news-category.service';
import { NewsCategoryController } from './categories/controllers/news-category.controller';
import { PublicNewsCategoryController } from './categories/controllers/public-news-category.controller';

// Article entities, services, and controllers
import { NewsArticle } from './articles/entities/news-article.entity';
import { NewsArticleService } from './articles/services/news-article.service';
import { NewsArticleController } from './articles/controllers/news-article.controller';
import { PublicNewsArticleController } from './articles/controllers/public-news-article.controller';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            NewsCategory,
            NewsArticle,
        ]),
    ],
    controllers: [
        // Category controllers
        NewsCategoryController,
        PublicNewsCategoryController,
        // Article controllers
        NewsArticleController,
        PublicNewsArticleController,
    ],
    providers: [
        // Category services
        NewsCategoryService,
        // Article services
        NewsArticleService,
    ],
    exports: [
        // Export services for use in other modules
        NewsCategoryService,
        NewsArticleService,
    ],
})
export class NewsModule { }
