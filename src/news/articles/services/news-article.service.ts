import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { NewsArticle, ArticleStatus } from '../entities/news-article.entity';
import { NewsCategory } from '../../categories/entities/news-category.entity';
import { CacheService } from '../../../core/cache/cache.service';
import { League } from '../../../sports/football/models/league.entity';
import { Team } from '../../../sports/football/models/team.entity';
import { Player } from '../../../sports/football/models/player.entity';
import { Fixture } from '../../../sports/football/models/fixture.entity';
import { SystemUser } from '../../../auth/system/entities/system-user.entity';
import {
    CreateArticleDto,
    UpdateArticleDto,
    GetArticlesDto,
    ArticleResponseDto,
    PaginatedArticlesResponseDto,
    PublicArticlesDto
} from '../dto';

@Injectable()
export class NewsArticleService {
    private readonly logger = new Logger(NewsArticleService.name);
    private readonly CACHE_TTL = 900; // 15 minutes
    private readonly CACHE_PREFIX = 'news_article';

    constructor(
        @InjectRepository(NewsArticle)
        private readonly articleRepository: Repository<NewsArticle>,
        @InjectRepository(NewsCategory)
        private readonly categoryRepository: Repository<NewsCategory>,
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        @InjectRepository(Team)
        private readonly teamRepository: Repository<Team>,
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        @InjectRepository(SystemUser)
        private readonly systemUserRepository: Repository<SystemUser>,
        private readonly cacheService: CacheService,
    ) { }

    /**
     * Create a new news article
     */
    async createArticle(dto: CreateArticleDto, authorId: number): Promise<ArticleResponseDto> {
        this.logger.debug(`Creating article: ${dto.title}`);

        // Verify category exists
        const category = await this.categoryRepository.findOne({
            where: { id: dto.categoryId }
        });
        if (!category) {
            throw new BadRequestException(`Category with ID ${dto.categoryId} not found`);
        }

        // Generate slug if not provided
        const slug = dto.slug || this.generateSlug(dto.title);

        // Check if slug already exists
        const existingArticle = await this.articleRepository.findOne({
            where: { slug }
        });
        if (existingArticle) {
            throw new BadRequestException(`Article with slug '${slug}' already exists`);
        }

        // Create article
        const article = this.articleRepository.create({
            ...dto,
            slug,
            authorId,
            publishedAt: dto.status === ArticleStatus.PUBLISHED
                ? (dto.publishedAt || new Date())
                : undefined,
        });

        const savedArticle = await this.articleRepository.save(article);

        // Update category article count
        await this.updateCategoryArticleCount(dto.categoryId);

        // Clear cache
        await this.clearArticleCache();

        this.logger.log(`Article created: ${savedArticle.slug} (ID: ${savedArticle.id})`);

        const articleWithCategory = await this.getArticleWithCategory(savedArticle.id);
        if (!articleWithCategory) {
            throw new Error('Failed to retrieve created article');
        }

        return this.mapToResponseDto(articleWithCategory);
    }

    /**
     * Update an existing article
     */
    async updateArticle(id: number, dto: UpdateArticleDto, userId: number): Promise<ArticleResponseDto> {
        console.log(`🔍 SERVICE: Starting updateArticle - ID: ${id}, DTO:`, JSON.stringify(dto), `User: ${userId}`);
        this.logger.debug(`Updating article ID: ${id}`);

        const article = await this.articleRepository.findOne({
            where: { id },
            relations: ['category']
        });
        console.log(`🔍 SERVICE: Found article:`, article ? `ID ${article.id}, categoryId: ${article.categoryId}` : 'NOT FOUND');
        if (!article) {
            throw new NotFoundException(`Article with ID ${id} not found`);
        }

        // Verify category exists if changing
        if (dto.categoryId && dto.categoryId !== article.categoryId) {
            const category = await this.categoryRepository.findOne({
                where: { id: dto.categoryId }
            });
            if (!category) {
                throw new BadRequestException(`Category with ID ${dto.categoryId} not found`);
            }
        }

        const oldCategoryId = article.categoryId;
        const wasPublished = article.status === ArticleStatus.PUBLISHED;

        // Handle publishedAt logic
        let publishedAt = article.publishedAt;
        if (dto.status === ArticleStatus.PUBLISHED && !wasPublished) {
            // Publishing for the first time
            publishedAt = dto.publishedAt || new Date();
        } else if (dto.status === ArticleStatus.DRAFT && wasPublished) {
            // Unpublishing - keep publishedAt for history
            publishedAt = article.publishedAt;
        } else if (dto.publishedAt !== undefined) {
            // Explicitly setting publishedAt
            publishedAt = dto.publishedAt;
        }

        // Update article with all fields explicitly
        this.logger.log(`🔍 DEBUG: DTO received: ${JSON.stringify(dto)}`);
        this.logger.log(`🔍 DEBUG: Article before update - categoryId: ${article.categoryId}`);

        if (dto.title !== undefined) article.title = dto.title;
        if (dto.excerpt !== undefined) article.excerpt = dto.excerpt;
        if (dto.content !== undefined) article.content = dto.content;
        if (dto.featuredImage !== undefined) article.featuredImage = dto.featuredImage;
        if (dto.tags !== undefined) article.tags = dto.tags;
        if (dto.status !== undefined) article.status = dto.status;
        if (dto.metaTitle !== undefined) article.metaTitle = dto.metaTitle;
        if (dto.metaDescription !== undefined) article.metaDescription = dto.metaDescription;
        if (dto.relatedLeagueId !== undefined) article.relatedLeagueId = dto.relatedLeagueId;
        if (dto.relatedTeamId !== undefined) article.relatedTeamId = dto.relatedTeamId;
        if (dto.relatedPlayerId !== undefined) article.relatedPlayerId = dto.relatedPlayerId;
        if (dto.relatedFixtureId !== undefined) article.relatedFixtureId = dto.relatedFixtureId;
        if (dto.isFeatured !== undefined) article.isFeatured = dto.isFeatured;
        if (dto.priority !== undefined) article.priority = dto.priority;
        if (dto.categoryId !== undefined) {
            this.logger.log(`🔍 DEBUG: Updating categoryId from ${article.categoryId} to ${dto.categoryId}`);
            article.categoryId = dto.categoryId;
        }

        article.updatedBy = userId;
        article.publishedAt = publishedAt;

        this.logger.log(`🔍 DEBUG: Article after update - categoryId: ${article.categoryId}`);

        // Use update() instead of save() to ensure database update
        await this.articleRepository.update(id, {
            categoryId: article.categoryId,
            title: article.title,
            content: article.content,
            excerpt: article.excerpt,
            featuredImage: article.featuredImage,
            tags: article.tags,
            status: article.status,
            metaTitle: article.metaTitle,
            metaDescription: article.metaDescription,
            relatedLeagueId: article.relatedLeagueId,
            relatedTeamId: article.relatedTeamId,
            relatedPlayerId: article.relatedPlayerId,
            relatedFixtureId: article.relatedFixtureId,
            isFeatured: article.isFeatured,
            priority: article.priority,
            updatedBy: article.updatedBy,
            publishedAt: article.publishedAt
        });
        console.log(`🔍 SERVICE: After update() - categoryId should be: ${article.categoryId}`);

        // Update category counts if category changed
        if (dto.categoryId && dto.categoryId !== oldCategoryId) {
            await this.updateCategoryArticleCount(oldCategoryId);
            await this.updateCategoryArticleCount(dto.categoryId);
        } else {
            await this.updateCategoryArticleCount(article.categoryId);
        }

        // Clear cache
        await this.clearArticleCache();

        this.logger.log(`Article updated: ${article.slug} (ID: ${article.id})`);

        const articleWithCategory = await this.getArticleWithCategory(article.id);
        console.log(`🔍 SERVICE: Re-fetched article categoryId: ${articleWithCategory?.categoryId}`);
        if (!articleWithCategory) {
            throw new Error('Failed to retrieve updated article');
        }

        return this.mapToResponseDto(articleWithCategory);
    }

    /**
     * Delete an article
     */
    async deleteArticle(id: number): Promise<void> {
        this.logger.debug(`Deleting article ID: ${id}`);

        const article = await this.articleRepository.findOne({ where: { id } });
        if (!article) {
            throw new NotFoundException(`Article with ID ${id} not found`);
        }

        const categoryId = article.categoryId;
        await this.articleRepository.remove(article);

        // Update category article count
        await this.updateCategoryArticleCount(categoryId);

        // Clear cache
        await this.clearArticleCache();

        this.logger.log(`Article deleted: ${article.slug} (ID: ${id})`);
    }

    /**
     * Get articles with pagination and filtering
     */
    async getArticles(query: GetArticlesDto): Promise<PaginatedArticlesResponseDto> {
        const { page = 1, limit = 10 } = query;

        // Try cache first
        const cacheKey = `${this.CACHE_PREFIX}_list_${JSON.stringify(query)}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for articles list: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // Build query
        const queryBuilder = this.createArticleQueryBuilder();
        this.applyFilters(queryBuilder, query);
        this.applySorting(queryBuilder, query);

        // Apply pagination
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);

        // Execute query
        const [articles, totalItems] = await queryBuilder.getManyAndCount();

        const response: PaginatedArticlesResponseDto = {
            data: articles.map(article => this.mapToResponseDto(article)),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);
        this.logger.debug(`Cached articles list: ${cacheKey} (TTL: ${this.CACHE_TTL}s)`);

        return response;
    }

    /**
     * Get article by ID
     */
    async getArticleById(id: number): Promise<ArticleResponseDto> {
        const cacheKey = `${this.CACHE_PREFIX}_id_${id}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for article ID: ${id}`);
            return JSON.parse(cached);
        }

        const article = await this.getArticleWithCategory(id);
        if (!article) {
            throw new NotFoundException(`Article with ID ${id} not found`);
        }

        const response = this.mapToResponseDto(article);

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Get article by slug
     */
    async getArticleBySlug(slug: string): Promise<ArticleResponseDto> {
        const cacheKey = `${this.CACHE_PREFIX}_slug_${slug}`;
        // Temporarily disable cache for testing
        // const cached = await this.cacheService.getCache(cacheKey);
        // if (cached) {
        //     this.logger.debug(`Cache hit for article slug: ${slug}`);
        //     return JSON.parse(cached);
        // }

        const article = await this.articleRepository.findOne({
            where: { slug },
            relations: ['category']
        });
        if (!article) {
            throw new NotFoundException(`Article with slug '${slug}' not found`);
        }

        const response = await this.mapToResponseDtoWithRelatedData(article);

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Get published articles for public access
     */
    async getPublishedArticles(query: PublicArticlesDto): Promise<PaginatedArticlesResponseDto> {
        const { page = 1, limit = 10, search, tags, sortBy = 'publishedAt', sortOrder = 'DESC' } = query;

        const cacheKey = `${this.CACHE_PREFIX}_public_${JSON.stringify(query)}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for public articles: ${cacheKey}`);
            return JSON.parse(cached);
        }

        const queryBuilder = this.createArticleQueryBuilder()
            .where('article.status = :status', { status: ArticleStatus.PUBLISHED })
            .andWhere('article.publishedAt IS NOT NULL');

        // Apply search
        if (search) {
            queryBuilder.andWhere(
                'article.searchVector @@ plainto_tsquery(:search)',
                { search }
            );
        }

        // Apply tag filter
        if (tags) {
            const tagArray = tags.split(',').map(tag => tag.trim());
            queryBuilder.andWhere(
                'article.tags && :tags',
                { tags: tagArray }
            );
        }

        // Apply sorting
        if (sortBy === 'publishedAt') {
            queryBuilder.orderBy('article.publishedAt', sortOrder);
        } else if (sortBy === 'viewCount') {
            queryBuilder.orderBy('article.viewCount', sortOrder);
        } else if (sortBy === 'priority') {
            queryBuilder.orderBy('article.priority', sortOrder);
        }

        // Apply pagination
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);

        const [articles, totalItems] = await queryBuilder.getManyAndCount();

        const response: PaginatedArticlesResponseDto = {
            data: articles.map(article => this.mapToResponseDto(article)),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Get articles by category slug
     */
    async getArticlesByCategory(categorySlug: string, query: PublicArticlesDto): Promise<PaginatedArticlesResponseDto> {
        const { page = 1, limit = 10 } = query;

        const cacheKey = `${this.CACHE_PREFIX}_category_${categorySlug}_${JSON.stringify(query)}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }

        const queryBuilder = this.createArticleQueryBuilder()
            .where('article.status = :status', { status: ArticleStatus.PUBLISHED })
            .andWhere('category.slug = :categorySlug', { categorySlug })
            .andWhere('article.publishedAt IS NOT NULL');

        // Apply search and filters from query
        if (query.search) {
            queryBuilder.andWhere('article.searchVector @@ plainto_tsquery(:search)', { search: query.search });
        }

        if (query.tags) {
            const tagArray = query.tags.split(',').map(tag => tag.trim());
            queryBuilder.andWhere('article.tags && :tags', { tags: tagArray });
        }

        // Apply sorting
        if (query.sortBy === 'publishedAt') {
            queryBuilder.orderBy('article.publishedAt', query.sortOrder);
        } else if (query.sortBy === 'viewCount') {
            queryBuilder.orderBy('article.viewCount', query.sortOrder);
        } else if (query.sortBy === 'priority') {
            queryBuilder.orderBy('article.priority', query.sortOrder);
        }

        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);

        const [articles, totalItems] = await queryBuilder.getManyAndCount();

        const response: PaginatedArticlesResponseDto = {
            data: articles.map(article => this.mapToResponseDto(article)),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);
        return response;
    }

    /**
     * Get featured articles
     */
    async getFeaturedArticles(limit: number = 5): Promise<ArticleResponseDto[]> {
        const cacheKey = `${this.CACHE_PREFIX}_featured_${limit}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }

        const articles = await this.createArticleQueryBuilder()
            .where('article.status = :status', { status: ArticleStatus.PUBLISHED })
            .andWhere('article.isFeatured = :featured', { featured: true })
            .andWhere('article.publishedAt IS NOT NULL')
            .orderBy('article.priority', 'DESC')
            .addOrderBy('article.publishedAt', 'DESC')
            .take(limit)
            .getMany();

        const response = articles.map(article => this.mapToResponseDto(article));
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Get related articles
     */
    async getRelatedArticles(articleId: number, limit: number = 5): Promise<ArticleResponseDto[]> {
        const article = await this.articleRepository.findOne({
            where: { id: articleId },
            relations: ['category']
        });

        if (!article) {
            return [];
        }

        const queryBuilder = this.createArticleQueryBuilder()
            .where('article.status = :status', { status: ArticleStatus.PUBLISHED })
            .andWhere('article.id != :articleId', { articleId })
            .andWhere('article.publishedAt IS NOT NULL');

        // Prioritize same category
        queryBuilder.andWhere('article.categoryId = :categoryId', { categoryId: article.categoryId });

        // If article has tags, find articles with similar tags
        if (article.tags && article.tags.length > 0) {
            queryBuilder.orWhere('article.tags && :tags', { tags: article.tags });
        }

        const relatedArticles = await queryBuilder
            .orderBy('article.publishedAt', 'DESC')
            .take(limit)
            .getMany();

        return relatedArticles.map(article => this.mapToResponseDto(article));
    }

    /**
     * Publish article
     */
    async publishArticle(id: number): Promise<ArticleResponseDto> {
        const article = await this.getArticleWithCategory(id);
        if (!article) {
            throw new NotFoundException(`Article with ID ${id} not found`);
        }

        article.status = ArticleStatus.PUBLISHED;
        article.publishedAt = new Date();

        const updatedArticle = await this.articleRepository.save(article);
        await this.updateCategoryArticleCount(article.categoryId);
        await this.clearArticleCache();

        this.logger.log(`Article published: ${article.slug}`);
        return this.mapToResponseDto(updatedArticle);
    }

    /**
     * Unpublish article
     */
    async unpublishArticle(id: number): Promise<ArticleResponseDto> {
        const article = await this.getArticleWithCategory(id);
        if (!article) {
            throw new NotFoundException(`Article with ID ${id} not found`);
        }

        article.status = ArticleStatus.DRAFT;
        const updatedArticle = await this.articleRepository.save(article);
        await this.updateCategoryArticleCount(article.categoryId);
        await this.clearArticleCache();

        this.logger.log(`Article unpublished: ${article.slug}`);
        return this.mapToResponseDto(updatedArticle);
    }

    /**
     * Archive article
     */
    async archiveArticle(id: number): Promise<ArticleResponseDto> {
        const article = await this.getArticleWithCategory(id);
        if (!article) {
            throw new NotFoundException(`Article with ID ${id} not found`);
        }

        article.status = ArticleStatus.ARCHIVED;
        const updatedArticle = await this.articleRepository.save(article);
        await this.updateCategoryArticleCount(article.categoryId);
        await this.clearArticleCache();

        this.logger.log(`Article archived: ${article.slug}`);
        return this.mapToResponseDto(updatedArticle);
    }

    /**
     * Increment view count
     */
    async incrementViewCount(id: number): Promise<void> {
        await this.articleRepository.increment({ id }, 'viewCount', 1);
        await this.clearArticleCache();
    }

    /**
     * Increment share count
     */
    async incrementShareCount(id: number): Promise<void> {
        await this.articleRepository.increment({ id }, 'shareCount', 1);
        await this.clearArticleCache();
    }

    // ===== PRIVATE HELPER METHODS =====

    /**
     * Create base query builder with category join
     */
    private createArticleQueryBuilder(): SelectQueryBuilder<NewsArticle> {
        return this.articleRepository.createQueryBuilder('article')
            .leftJoinAndSelect('article.category', 'category');
    }

    /**
     * Apply filters to query builder
     */
    private applyFilters(queryBuilder: SelectQueryBuilder<NewsArticle>, query: GetArticlesDto): void {
        if (query.status) {
            queryBuilder.andWhere('article.status = :status', { status: query.status });
        }

        if (query.categoryId) {
            queryBuilder.andWhere('article.categoryId = :categoryId', { categoryId: query.categoryId });
        }

        if (query.categorySlug) {
            queryBuilder.andWhere('category.slug = :categorySlug', { categorySlug: query.categorySlug });
        }

        if (query.authorId) {
            queryBuilder.andWhere('article.authorId = :authorId', { authorId: query.authorId });
        }

        if (query.isFeatured !== undefined) {
            queryBuilder.andWhere('article.isFeatured = :isFeatured', { isFeatured: query.isFeatured });
        }

        if (query.relatedLeagueId) {
            queryBuilder.andWhere('article.relatedLeagueId = :relatedLeagueId', { relatedLeagueId: query.relatedLeagueId });
        }

        if (query.relatedTeamId) {
            queryBuilder.andWhere('article.relatedTeamId = :relatedTeamId', { relatedTeamId: query.relatedTeamId });
        }

        if (query.relatedPlayerId) {
            queryBuilder.andWhere('article.relatedPlayerId = :relatedPlayerId', { relatedPlayerId: query.relatedPlayerId });
        }

        if (query.relatedFixtureId) {
            queryBuilder.andWhere('article.relatedFixtureId = :relatedFixtureId', { relatedFixtureId: query.relatedFixtureId });
        }

        if (query.search) {
            queryBuilder.andWhere('article.searchVector @@ plainto_tsquery(:search)', { search: query.search });
        }

        if (query.tags) {
            const tagArray = query.tags.split(',').map(tag => tag.trim());
            queryBuilder.andWhere('article.tags && :tags', { tags: tagArray });
        }

        if (query.dateFrom) {
            queryBuilder.andWhere('article.createdAt >= :dateFrom', { dateFrom: new Date(query.dateFrom) });
        }

        if (query.dateTo) {
            queryBuilder.andWhere('article.createdAt <= :dateTo', { dateTo: new Date(query.dateTo) });
        }
    }

    /**
     * Apply sorting to query builder
     */
    private applySorting(queryBuilder: SelectQueryBuilder<NewsArticle>, query: GetArticlesDto): void {
        const { sortBy = 'createdAt', sortOrder = 'DESC' } = query;

        switch (sortBy) {
            case 'createdAt':
                queryBuilder.orderBy('article.createdAt', sortOrder);
                break;
            case 'updatedAt':
                queryBuilder.orderBy('article.updatedAt', sortOrder);
                break;
            case 'publishedAt':
                queryBuilder.orderBy('article.publishedAt', sortOrder);
                break;
            case 'title':
                queryBuilder.orderBy('article.title', sortOrder);
                break;
            case 'priority':
                queryBuilder.orderBy('article.priority', sortOrder);
                break;
            case 'viewCount':
                queryBuilder.orderBy('article.viewCount', sortOrder);
                break;
            default:
                queryBuilder.orderBy('article.createdAt', 'DESC');
        }
    }

    /**
     * Get article with category relationship
     */
    private async getArticleWithCategory(id: number): Promise<NewsArticle | null> {
        return this.articleRepository
            .createQueryBuilder('article')
            .leftJoinAndSelect('article.category', 'category')
            .where('article.id = :id', { id })
            .getOne();
    }

    /**
     * Generate slug from title
     */
    private generateSlug(title: string): string {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .trim()
            .substring(0, 600); // Limit length
    }

    /**
     * Map article entity to response DTO with related data for public endpoints
     */
    private async mapToResponseDtoWithRelatedData(article: NewsArticle): Promise<ArticleResponseDto> {
        this.logger.debug(`🔍 Fetching related data for article ${article.id}`);

        // Start with basic mapping
        const basicResponse = this.mapToResponseDto(article);

        // Fetch related data
        const relatedData: any = {};

        try {
            // Fetch author info
            if (article.authorId) {
                const author = await this.systemUserRepository.findOne({
                    where: { id: article.authorId }
                });
                if (author) {
                    relatedData.author = {
                        id: author.id,
                        username: author.username,
                        fullName: author.fullName || author.username,
                        role: author.role
                    };
                }
            }

            // Fetch related league
            if (article.relatedLeagueId) {
                const league = await this.leagueRepository.findOne({
                    where: { id: article.relatedLeagueId }
                });
                if (league) {
                    relatedData.relatedLeague = {
                        id: league.id,
                        externalId: (league as any).externalId,
                        name: (league as any).name,
                        country: (league as any).country,
                        logo: (league as any).logo,
                        season: (league as any).season
                    };
                }
            }

            // Fetch related team
            if (article.relatedTeamId) {
                const team = await this.teamRepository.findOne({
                    where: { id: article.relatedTeamId }
                });
                if (team) {
                    relatedData.relatedTeam = {
                        id: team.id,
                        externalId: (team as any).externalId,
                        name: (team as any).name,
                        code: (team as any).code,
                        country: (team as any).country,
                        logo: (team as any).logo
                    };
                }
            }

            // Fetch related player
            if (article.relatedPlayerId) {
                const player = await this.playerRepository.findOne({
                    where: { id: article.relatedPlayerId }
                });
                if (player) {
                    relatedData.relatedPlayer = {
                        id: player.id,
                        externalId: (player as any).externalId,
                        name: (player as any).name,
                        age: (player as any).age,
                        nationality: (player as any).nationality,
                        photo: (player as any).photo
                    };
                }
            }

            // Fetch related fixture
            if (article.relatedFixtureId) {
                const fixture = await this.fixtureRepository.findOne({
                    where: { id: article.relatedFixtureId },
                    relations: ['homeTeam', 'awayTeam', 'league']
                });
                if (fixture) {
                    relatedData.relatedFixture = {
                        id: fixture.id,
                        externalId: (fixture as any).externalId,
                        leagueName: (fixture as any).league?.name || 'Unknown League',
                        matchTitle: `${(fixture as any).homeTeam?.name || 'Home'} vs ${(fixture as any).awayTeam?.name || 'Away'}`,
                        date: (fixture as any).date,
                        status: (fixture as any).status
                    };
                }
            }
        } catch (error) {
            this.logger.warn(`Error fetching related data for article ${article.id}:`, error);
        }

        this.logger.debug(`🔍 Related data fetched:`, relatedData);

        // Merge basic response with related data
        return {
            ...basicResponse,
            ...relatedData
        };
    }



    /**
     * Map entity to response DTO
     */
    private mapToResponseDto(article: NewsArticle): ArticleResponseDto {
        // Workaround: Use category.id if categoryId is undefined
        const categoryId = article.categoryId || article.category?.id;

        return {
            id: article.id,
            title: article.title,
            slug: article.slug,
            excerpt: article.excerpt,
            content: article.content,
            featuredImage: article.featuredImage,
            tags: article.tags,
            status: article.status,
            publishedAt: article.publishedAt,
            metaTitle: article.metaTitle,
            metaDescription: article.metaDescription,
            relatedLeagueId: article.relatedLeagueId,
            relatedTeamId: article.relatedTeamId,
            relatedPlayerId: article.relatedPlayerId,
            relatedFixtureId: article.relatedFixtureId,
            viewCount: article.viewCount,
            shareCount: article.shareCount,
            likeCount: article.likeCount,
            isFeatured: article.isFeatured,
            priority: article.priority,
            categoryId: categoryId, // ← Use workaround value
            category: {
                id: article.category.id,
                slug: article.category.slug,
                name: article.category.name,
                description: article.category.description,
                icon: article.category.icon,
                color: article.category.color,
                sortOrder: article.category.sortOrder,
                isActive: article.category.isActive,
                isPublic: article.category.isPublic,
                metaTitle: article.category.metaTitle,
                metaDescription: article.category.metaDescription,
                articleCount: article.category.articleCount,
                publishedArticleCount: article.category.publishedArticleCount,
                createdAt: article.category.createdAt,
                updatedAt: article.category.updatedAt,
            },
            authorId: article.authorId,
            createdAt: article.createdAt,
            updatedAt: article.updatedAt,
        };
    }

    /**
     * Update category article counts
     */
    private async updateCategoryArticleCount(categoryId: number): Promise<void> {
        const totalCount = await this.articleRepository.count({
            where: { categoryId }
        });

        const publishedCount = await this.articleRepository.count({
            where: { categoryId, status: ArticleStatus.PUBLISHED }
        });

        await this.categoryRepository.update(categoryId, {
            articleCount: totalCount,
            publishedArticleCount: publishedCount,
        });
    }

    /**
     * Clear all article-related cache
     */
    private async clearArticleCache(): Promise<void> {
        await this.cacheService.deleteByPattern(`${this.CACHE_PREFIX}_*`);
        await this.cacheService.deleteByPattern('news_category_*'); // Also clear category cache
        this.logger.debug('Article cache cleared');
    }
}