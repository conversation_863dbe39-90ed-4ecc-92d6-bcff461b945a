import {
    IsString,
    IsOptional,
    <PERSON>Int,
    <PERSON><PERSON><PERSON>y,
    <PERSON>,
    Min,
    <PERSON>,
    <PERSON>Enum,
    IsBoolean
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArticleStatus } from '../entities/news-article.entity';

export class CreateArticleDto {
    @ApiProperty({
        description: 'Article title',
        example: '<PERSON><PERSON> Signs New Contract with Barcelona',
        minLength: 5,
        maxLength: 500
    })
    @IsString()
    @Length(5, 500)
    title: string;

    @ApiPropertyOptional({
        description: 'Article slug (auto-generated if not provided)',
        example: 'messi-signs-new-contract-barcelona',
        maxLength: 600
    })
    @IsOptional()
    @IsString()
    @Length(5, 600)
    slug?: string;

    @ApiPropertyOptional({
        description: 'Short excerpt/summary of the article',
        example: '<PERSON> has agreed to a new 3-year contract with Barcelona...'
    })
    @IsOptional()
    @IsString()
    excerpt?: string;

    @ApiProperty({
        description: 'Full article content (HTML supported)',
        example: '<p><PERSON> has officially signed a new contract...</p>'
    })
    @IsString()
    @Length(10, 50000)
    content: string;

    @ApiPropertyOptional({
        description: 'Featured image URL',
        example: 'https://example.com/images/messi-contract.jpg'
    })
    @IsOptional()
    @IsString()
    @Length(1, 1000)
    featuredImage?: string;

    @ApiPropertyOptional({
        description: 'Article tags for categorization',
        example: ['messi', 'barcelona', 'contract', 'transfer'],
        type: [String]
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    tags?: string[];

    @ApiPropertyOptional({
        description: 'Article status',
        example: ArticleStatus.DRAFT,
        enum: ArticleStatus,
        default: ArticleStatus.DRAFT
    })
    @IsOptional()
    @IsEnum(ArticleStatus)
    status?: ArticleStatus;

    @ApiProperty({
        description: 'Category ID for the article',
        example: 1
    })
    @IsInt()
    @Min(1)
    categoryId: number;

    @ApiPropertyOptional({
        description: 'SEO meta title',
        example: 'Messi Signs New Barcelona Contract - Latest Football News',
        maxLength: 300
    })
    @IsOptional()
    @IsString()
    @Length(1, 300)
    metaTitle?: string;

    @ApiPropertyOptional({
        description: 'SEO meta description',
        example: 'Breaking news: Lionel Messi has signed a new 3-year contract with Barcelona. Get all the details about the deal and what it means for the club.'
    })
    @IsOptional()
    @IsString()
    metaDescription?: string;

    @ApiPropertyOptional({
        description: 'Related league ID',
        example: 39
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    relatedLeagueId?: number;

    @ApiPropertyOptional({
        description: 'Related team ID',
        example: 529
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    relatedTeamId?: number;

    @ApiPropertyOptional({
        description: 'Related player ID',
        example: 154
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    relatedPlayerId?: number;

    @ApiPropertyOptional({
        description: 'Related fixture ID',
        example: 1234567
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    relatedFixtureId?: number;

    @ApiPropertyOptional({
        description: 'Whether article is featured',
        example: false,
        default: false
    })
    @IsOptional()
    @IsBoolean()
    isFeatured?: boolean;

    @ApiPropertyOptional({
        description: 'Article priority (higher = more important)',
        example: 0,
        minimum: 0,
        maximum: 100,
        default: 0
    })
    @IsOptional()
    @IsInt()
    @Min(0)
    @Max(100)
    priority?: number;
}
