import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { setupSwagger } from './docs/swagger.config';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { CustomLoggerService } from './common/logger/custom-logger.service';
import { RedisIoAdapter } from './adapters/redis-io.adapter';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    // ✅ OPTIMIZED LOGGING: Sử dụng minimal logging trong production
    logger: process.env.NODE_ENV === 'production'
      ? ['error', 'warn'] // Chỉ log errors và warnings trong production
      : new CustomLoggerService(),
  });

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // Get configuration
  const configService = app.get(ConfigService);
  const config = configService.get('app');

  // Serve static files for uploaded images
  const imageStoragePath = configService.get<string>('IMAGE_STORAGE_PATH', './public/images');
  app.useStaticAssets(join(process.cwd(), imageStoragePath), {
    prefix: '/uploads/',
  });

  // Setup Swagger documentation
  setupSwagger(app, config);

  // Setup Redis adapter for WebSocket clustering in production
  if (process.env.NODE_ENV === 'production') {
    const redisIoAdapter = await RedisIoAdapter.create(app);
    app.useWebSocketAdapter(redisIoAdapter);
  }

  const port = process.env.PORT || 3000;

  if (process.env.NODE_ENV !== 'production') {
    console.log('APISportsGame started');
    console.log(`📚 API Documentation available at: http://localhost:${port}/api-docs`);
    console.log(`🖼️  Uploaded images available at: http://localhost:${port}/uploads/`);
  }

  await app.listen(port);
}
bootstrap();