import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateNewsCategoriesTable1748600000000 implements MigrationInterface {
    name = 'CreateNewsCategoriesTable1748600000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create news_categories table
        await queryRunner.createTable(
            new Table({
                name: 'news_categories',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'slug',
                        type: 'varchar',
                        length: '100',
                        isUnique: true,
                    },
                    {
                        name: 'name',
                        type: 'varchar',
                        length: '200',
                    },
                    {
                        name: 'description',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'icon',
                        type: 'varchar',
                        length: '500',
                        isNullable: true,
                    },
                    {
                        name: 'color',
                        type: 'varchar',
                        length: '7',
                        isNullable: true,
                    },
                    {
                        name: 'sortOrder',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'isActive',
                        type: 'boolean',
                        default: true,
                    },
                    {
                        name: 'isPublic',
                        type: 'boolean',
                        default: true,
                    },
                    {
                        name: 'metaTitle',
                        type: 'varchar',
                        length: '300',
                        isNullable: true,
                    },
                    {
                        name: 'metaDescription',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'articleCount',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'publishedArticleCount',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'createdBy',
                        type: 'int',
                    },
                    {
                        name: 'updatedBy',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                        onUpdate: 'CURRENT_TIMESTAMP',
                    },
                ],
            }),
            true,
        );

        // Create indexes
        await queryRunner.createIndex(
            'news_categories',
            new TableIndex({ name: 'idx_news_category_slug', columnNames: ['slug'] }),
        );

        await queryRunner.createIndex(
            'news_categories',
            new TableIndex({ name: 'idx_news_category_sort_order', columnNames: ['sortOrder'] }),
        );

        await queryRunner.createIndex(
            'news_categories',
            new TableIndex({ name: 'idx_news_category_active', columnNames: ['isActive'] }),
        );

        await queryRunner.createIndex(
            'news_categories',
            new TableIndex({ name: 'idx_news_category_public', columnNames: ['isPublic'] }),
        );

        // Insert default categories
        await queryRunner.query(`
            INSERT INTO news_categories (slug, name, description, icon, color, "sortOrder", "createdBy") VALUES
            ('transfer-news', 'Transfer News', 'Latest transfer rumors, confirmations and market updates', 'transfer', '#FF6B35', 1, 1),
            ('match-reports', 'Match Reports', 'Detailed analysis and reports from recent matches', 'match', '#4ECDC4', 2, 1),
            ('player-interviews', 'Player Interviews', 'Exclusive interviews with players and coaches', 'interview', '#45B7D1', 3, 1),
            ('league-updates', 'League Updates', 'News and updates from major football leagues', 'league', '#96CEB4', 4, 1),
            ('injury-news', 'Injury News', 'Player injury reports and recovery updates', 'medical', '#FFEAA7', 5, 1),
            ('tactical-analysis', 'Tactical Analysis', 'In-depth tactical breakdowns and analysis', 'tactics', '#DDA0DD', 6, 1),
            ('youth-football', 'Youth Football', 'News from youth academies and development', 'youth', '#98D8C8', 7, 1),
            ('womens-football', 'Women''s Football', 'Coverage of women''s football leagues and tournaments', 'womens', '#F7DC6F', 8, 1)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.dropIndex('news_categories', 'idx_news_category_public');
        await queryRunner.dropIndex('news_categories', 'idx_news_category_active');
        await queryRunner.dropIndex('news_categories', 'idx_news_category_sort_order');
        await queryRunner.dropIndex('news_categories', 'idx_news_category_slug');

        // Drop table
        await queryRunner.dropTable('news_categories');
    }
}
