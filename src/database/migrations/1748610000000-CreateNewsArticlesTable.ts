import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateNewsArticlesTable1748610000000 implements MigrationInterface {
    name = 'CreateNewsArticlesTable1748610000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create news_articles table
        await queryRunner.createTable(
            new Table({
                name: 'news_articles',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'title',
                        type: 'varchar',
                        length: '500',
                    },
                    {
                        name: 'slug',
                        type: 'varchar',
                        length: '600',
                        isUnique: true,
                    },
                    {
                        name: 'excerpt',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'content',
                        type: 'text',
                    },
                    {
                        name: 'featuredImage',
                        type: 'varchar',
                        length: '1000',
                        isNullable: true,
                    },
                    {
                        name: 'tags',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'status',
                        type: 'enum',
                        enum: ['draft', 'published', 'archived'],
                        default: "'draft'",
                    },
                    {
                        name: 'publishedAt',
                        type: 'timestamp',
                        isNullable: true,
                    },
                    {
                        name: 'metaTitle',
                        type: 'varchar',
                        length: '300',
                        isNullable: true,
                    },
                    {
                        name: 'metaDescription',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'relatedLeagueId',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'relatedTeamId',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'relatedPlayerId',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'relatedFixtureId',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'viewCount',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'shareCount',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'likeCount',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'isFeatured',
                        type: 'boolean',
                        default: false,
                    },
                    {
                        name: 'priority',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'categoryId',
                        type: 'int',
                    },
                    {
                        name: 'authorId',
                        type: 'int',
                    },
                    {
                        name: 'updatedBy',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                        onUpdate: 'CURRENT_TIMESTAMP',
                    },
                    {
                        name: 'searchVector',
                        type: 'tsvector',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // Create indexes
        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_title', columnNames: ['title'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_slug', columnNames: ['slug'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_status', columnNames: ['status'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_published_at', columnNames: ['publishedAt'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_league', columnNames: ['relatedLeagueId'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_team', columnNames: ['relatedTeamId'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_player', columnNames: ['relatedPlayerId'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_fixture', columnNames: ['relatedFixtureId'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_featured', columnNames: ['isFeatured'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_priority', columnNames: ['priority'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_category', columnNames: ['categoryId'] }),
        );

        await queryRunner.createIndex(
            'news_articles',
            new TableIndex({ name: 'idx_news_article_created_at', columnNames: ['createdAt'] }),
        );

        // Create foreign key constraint for category
        await queryRunner.createForeignKey(
            'news_articles',
            new TableForeignKey({
                columnNames: ['categoryId'],
                referencedColumnNames: ['id'],
                referencedTableName: 'news_categories',
                onDelete: 'RESTRICT', // Prevent category deletion if articles exist
            }),
        );

        // Create full-text search index (PostgreSQL specific)
        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_news_article_search
            ON news_articles USING gin(searchVector);
        `);

        // Create trigger to update search vector automatically
        await queryRunner.query(`
            CREATE OR REPLACE FUNCTION update_news_article_search_vector()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.searchVector := to_tsvector('english',
                    COALESCE(NEW.title, '') || ' ' ||
                    COALESCE(NEW.excerpt, '') || ' ' ||
                    COALESCE(NEW.content, '') || ' ' ||
                    COALESCE(array_to_string(string_to_array(NEW.tags, ','), ' '), '')
                );
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            CREATE TRIGGER trigger_update_news_article_search_vector
                BEFORE INSERT OR UPDATE ON news_articles
                FOR EACH ROW EXECUTE FUNCTION update_news_article_search_vector();
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop trigger and function
        await queryRunner.query(`
            DROP TRIGGER IF EXISTS trigger_update_news_article_search_vector ON news_articles;
            DROP FUNCTION IF EXISTS update_news_article_search_vector();
        `);

        // Drop foreign key
        const table = await queryRunner.getTable('news_articles');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('categoryId') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('news_articles', foreignKey);
            }
        }

        // Drop indexes
        await queryRunner.dropIndex('news_articles', 'idx_news_article_search');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_created_at');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_category');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_priority');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_featured');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_fixture');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_player');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_team');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_league');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_published_at');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_status');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_slug');
        await queryRunner.dropIndex('news_articles', 'idx_news_article_title');

        // Drop table
        await queryRunner.dropTable('news_articles');
    }
}
